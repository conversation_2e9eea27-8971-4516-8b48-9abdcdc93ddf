﻿<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-card">
        <div class="header-content">
          <i class="el-icon-chat-dot-round header-icon" />
          <span class="header-title">客服管理</span>
          <div class="connection-status" :class="{ connected: isConnected }">
            <i :class="isConnected ? 'el-icon-check' : 'el-icon-loading'" />
            <span>{{ isConnected ? "已连接" : "连接中..." }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="chat-container">
      <!-- 用户列表侧边栏 -->
      <div class="user-sidebar">
        <div class="sidebar-card">
          <div class="sidebar-header">
            <i class="el-icon-user-solid" />
            <span>用户列表</span>
            <el-badge
              :value="totalUnreadCount"
              :hidden="totalUnreadCount === 0"
            />
          </div>

          <div class="user-list-container">
            <div v-if="userList.length === 0" class="empty-state">
              <i class="el-icon-chat-dot-round" />
              <p>暂无用户消息</p>
            </div>

            <div
              v-for="(user, index) in userList"
              :key="index"
              class="user-item"
              :class="{ active: index === checkUser }"
              @click="checkUserHandler(index)"
            >
              <div class="user-avatar">
                <i class="el-icon-user-solid" />
              </div>
              <div class="user-info">
                <div class="user-phone">{{ user.phone }}</div>
                <div v-if="user.messageList.length > 0" class="last-message">
                  {{ user.messageList[user.messageList.length - 1].msg }}
                </div>
              </div>
              <div class="user-meta">
                <el-badge
                  :value="user.unread"
                  :hidden="user.unread === 0"
                />
                <div class="online-indicator" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 聊天区域 -->
      <div class="chat-main">
        <div class="chat-card">
          <!-- 聊天头部 -->
          <div
            v-if="userList.length > 0 && checkUser !== -1"
            class="chat-header"
          >
            <div class="chat-user-info">
              <div class="chat-avatar">
                <i class="el-icon-user-solid" />
              </div>
              <div class="chat-user-details">
                <div class="chat-user-phone">
                  {{ userList[checkUser].phone }}
                </div>
                <div class="chat-user-status">在线</div>
              </div>
            </div>
          </div>

          <!-- 聊天内容区 -->
          <div id="chatWrap" class="chat-content">
            <div
              v-if="userList.length === 0 || checkUser === -1"
              class="chat-empty"
            >
              <i class="el-icon-chat-dot-round" />
              <p>选择一个用户开始聊天</p>
            </div>

            <div v-else class="message-list">
              <div
                v-for="(message, index) in userList[checkUser].messageList"
                :key="index"
                class="message-item"
                :class="{
                  'admin-message': message.role === 'admin',
                  'user-message': message.role === 'user',
                }"
              >
                <div class="message-avatar">
                  <img
                    v-if="message.role === 'admin'"
                    src="@/assets/service/service.png"
                    alt="客服"
                  />
                  <img v-else src="@/assets/service/user.png" alt="用户" />
                </div>
                <div class="message-content">
                  <div class="message-bubble">
                    {{ message.msg }}
                  </div>
                  <div class="message-time">
                    {{ formatTime(new Date()) }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 消息输入区 -->
          <div
            v-if="userList.length > 0 && checkUser !== -1"
            class="chat-input"
          >
            <div class="input-container">
              <el-input
                v-model="textarea"
                type="textarea"
                :rows="3"
                placeholder="请输入消息内容..."
                @keyup.ctrl.enter.native="sendMessage"
              />
              <div class="input-actions">
                <div class="input-tip">
                  <span>Ctrl + Enter 发送</span>
                </div>
                <el-button
                  type="primary"
                  :disabled="!textarea.trim()"
                  icon="el-icon-position"
                  @click="sendMessage"
                >
                  发送
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getUserId } from "@/utils/auth";
import { setUserList, getUserList } from "@/utils/chat";

function Message(role = "", msg = "") {
  this.role = role;
  this.msg = msg;
  this.timestamp = new Date();
}

function User(from = "", phone = "") {
  this.from = from;
  this.phone = phone;
  this.messageList = [];
  this.unread = 0;
}

function SocketMessage(from = "", to = "", message = "") {
  this.from = from;
  this.to = to;
  this.message = message;
}

export default {
  data() {
    return {
      textarea: "",
      ws: null,
      userList: [],
      checkUser: -1,
      isConnected: false,
    };
  },
  computed: {
    totalUnreadCount() {
      return this.userList.reduce((total, user) => total + user.unread, 0);
    },
  },

  mounted() {
    this.initSocket();
    this.userList = getUserList();
  },

  beforeDestroy() {
    if (this.ws) {
      this.ws.close();
    }
  },
  methods: {
    initSocket() {
      const url =
        process.env.VUE_APP_BASE_API.replace("http", "ws") +
        "/websocket/" +
        getUserId();
      const ws = new WebSocket(url);
      this.ws = ws;

      // 连接成功
      ws.onopen = () => {
        this.isConnected = true;
        // WebSocket连接成功，不显示消息提示
      };

      // 心跳包
      const heartBeat = {
        from: "2",
        to: "system",
        phone: "null",
        message: "heart",
      };

      // 定时发起心跳包
      setInterval(() => {
        if (this.isConnected) {
          ws.send(JSON.stringify(heartBeat));
        }
      }, 30000);

      // 获取消息
      ws.onmessage = (e) => {
        const data = JSON.parse(e.data);
        const userId = data.from;
        const phone = data.phone + "";
        let index = -1;

        this.userList.forEach((o, i) => {
          if (o.from === userId) index = i;
        });

        // 如果用户不存在，创建新用户
        if (index === -1) {
          const user = new User(userId, phone);
          this.userList = [user, ...this.userList];
          index = 0;
          this.checkUser = this.checkUser + 1;
        } else {
          // 将有新消息的用户移到顶部
          let temp = this.checkUser;
          if (index > this.checkUser) {
            temp = this.checkUser + 1;
          }
          let shouldScrollToBottom = false;
          if (index === this.checkUser) {
            shouldScrollToBottom = true;
            temp = 0;
          }
          this.checkUser = temp;

          const userData = this.userList[index];
          this.userList.splice(index, 1);
          this.userList = [userData, ...this.userList];
          index = 0;

          if (shouldScrollToBottom) {
            this.$nextTick(() => {
              this.toBottom();
            });
          }
        }

        // 未读消息计数
        if (index !== this.checkUser) {
          this.userList[index].unread += 1;
        }

        // 添加消息到用户消息列表
        this.userList[index].messageList = [
          ...this.userList[index].messageList,
          new Message("user", data.message),
        ];
        setUserList(this.userList);
      };

      // 连接错误
      ws.onerror = () => {
        this.isConnected = false;
        this.$message.error("WebSocket连接异常，正在重新连接...");
        setTimeout(() => {
          this.initSocket();
        }, 3000);
      };

      // 连接关闭
      ws.onclose = () => {
        this.isConnected = false;
        // WebSocket连接已断开，不显示消息提示
      };
    },

    checkUserHandler(index) {
      this.checkUser = index;
      this.userList[index].unread = 0;
      this.$nextTick(() => {
        this.toBottom();
      });
      setUserList(this.userList);
    },

    sendMessage() {
      if (!this.textarea.trim()) {
        // 请输入消息内容，不显示消息提示
        return;
      }

      if (!this.isConnected) {
        this.$message.error("WebSocket未连接，无法发送消息");
        return;
      }

      const socketMessage = new SocketMessage(
        getUserId(),
        this.userList[this.checkUser].from,
        this.textarea
      );

      this.ws.send(JSON.stringify(socketMessage));
      const message = new Message("admin", this.textarea);
      this.userList[this.checkUser].messageList = [
        ...this.userList[this.checkUser].messageList,
        message,
      ];
      this.textarea = "";
      setUserList(this.userList);

      this.$nextTick(() => {
        this.toBottom();
      });
    },

    toBottom() {
      const node = document.getElementById("chatWrap");
      if (node) {
        const height = node.scrollHeight;
        node.scrollTo({
          left: 0,
          top: height,
          behavior: "smooth",
        });
      }
    },

    formatTime(date) {
      return date.toLocaleTimeString("zh-CN", {
        hour: "2-digit",
        minute: "2-digit",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 6px;
  background: transparent;
  height: calc(100vh - 84px);
  display: flex;
  flex-direction: column;
}

// 页面头部
.page-header {
  margin-bottom: 8px;
}

.header-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 8px 10px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .header-icon {
      font-size: 20px;
      color: #409eff;
      margin-right: 12px;
    }

    .header-title {
      font-size: 1.1rem;
      font-weight: 600;
      color: #374151;
      flex: 1;
    }

    .connection-status {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 0.8rem;
      background: rgba(239, 68, 68, 0.1);
      color: #dc2626;

      &.connected {
        background: rgba(34, 197, 94, 0.1);
        color: #059669;
      }

      i {
        font-size: 12px;
      }
    }
  }
}

// 聊天容器
.chat-container {
  display: flex;
  gap: 12px;
  flex: 1;
  min-height: 0;
}

// 用户侧边栏
.user-sidebar {
  width: 300px;
  flex-shrink: 0;
}

.sidebar-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  height: 100%;
  display: flex;
  flex-direction: column;

  .sidebar-header {
    display: flex;
    align-items: center;
    padding: 8px 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    font-weight: 600;
    color: #374151;

    i {
      margin-right: 6px;
      color: #409eff;
    }
  }

  .user-list-container {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #9ca3af;

  i {
    font-size: 48px;
    margin-bottom: 12px;
  }

  p {
    margin: 0;
    font-size: 0.8rem;
  }
}

.user-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 4px;

  &:hover {
    background: rgba(64, 158, 255, 0.05);
  }

  &.active {
    background: linear-gradient(
      135deg,
      rgba(64, 158, 255, 0.1) 0%,
      rgba(24, 144, 255, 0.1) 100%
    );
    border: 1px solid rgba(64, 158, 255, 0.2);
  }

  .user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 12px;

    i {
      font-size: 1rem;
    }
  }

  .user-info {
    flex: 1;
    min-width: 0;

    .user-phone {
      font-weight: 600;
      color: #374151;
      margin-bottom: 4px;
    }

    .last-message {
      font-size: 12px;
      color: #6b7280;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .user-meta {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;

    .online-indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #10b981;
    }
  }
}

// 聊天主区域
.chat-main {
  flex: 1;
  min-width: 0;
}

.chat-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-header {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);

  .chat-user-info {
    display: flex;
    align-items: center;

    .chat-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      margin-right: 12px;

      i {
        font-size: 1rem;
      }
    }

    .chat-user-details {
      .chat-user-phone {
        font-weight: 600;
        color: #374151;
      }

      .chat-user-status {
        font-size: 12px;
        color: #10b981;
      }
    }
  }
}

.chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 6px;
  min-height: 0;
}

.chat-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #9ca3af;

  i {
    font-size: 64px;
    margin-bottom: 6px;
  }

  p {
    margin: 0;
    font-size: 1rem;
  }
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.message-item {
  display: flex;
  align-items: flex-start;

  &.admin-message {
    flex-direction: row-reverse;

    .message-content {
      align-items: flex-end;

      .message-bubble {
        background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
        color: white;
      }
    }
  }

  .message-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 12px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .message-content {
    display: flex;
    flex-direction: column;
    max-width: 70%;

    .message-bubble {
      padding: 12px 16px;
      border-radius: 18px;
      background: #f1f5f9;
      color: #374151;
      word-wrap: break-word;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .message-time {
      font-size: 11px;
      color: #9ca3af;
      margin-top: 4px;
      padding: 0 4px;
    }
  }
}

// 输入区域
.chat-input {
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  padding: 8px 10px;

  .input-container {
    :deep(.el-textarea__inner) {
      border-radius: 8px;
      border: 1px solid #e5e7eb;
      resize: none;
      transition: all 0.3s ease;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
      }
    }

    .input-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .input-tip {
        font-size: 12px;
        color: #9ca3af;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .chat-container {
    flex-direction: column;
  }

  .user-sidebar {
    width: 100%;
    height: 200px;

    .user-list-container {
      .user-item {
        .last-message {
          display: none;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 4px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;

    .connection-status {
      align-self: flex-end;
    }
  }

  .message-item .message-content {
    max-width: 85%;
  }
}
</style>
