<template>
  <div class="credit-warn-container">
    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 内容卡片 -->
      <div class="content-card">
        <!-- 安全提示 -->
        <div class="section">
          <div class="section-title">
            <i class="el-icon-lock"></i>
            <span>充值须知</span>
          </div>

          <div class="tip-item important">
            <div class="tip-number">1</div>
            <div class="tip-content">
              <span class="tip-title">充值安全:</span>
              <span>本次充值未到账前。严禁同一号码在任何平台再次下单，如果有下单无售后。</span>
            </div>
          </div>

          <div class="tip-item">
            <div class="tip-number">2</div>
            <div class="tip-content">
              <span class="tip-title">防范诈骗:</span>
              <span>我们不会主动致电，如遇陌生来电需警惕。</span>
            </div>
          </div>

          <div class="tip-item">
            <div class="tip-number">3</div>
            <div class="tip-content">
              <span class="tip-title">客户支持:</span>
              <span>如有疑问，请通过官方平台联系在线客服。</span>
            </div>
          </div>

          <div class="general-tip">
            请严格遵守以上安全准则，共同维护信息安全。如需帮助，随时联系我们。
          </div>
        </div>

        <!-- 充值说明 -->
        <div class="section">
          <div class="section-title">
            <i class="el-icon-time"></i>
            <span>充值说明</span>
          </div>

          <div class="tip-item">
            <div class="tip-number">4</div>
            <div class="tip-content">
              <span>充值到账时间为0-48小时，建议月中充值，因为月初月尾会有延迟（不超过72小时），介意勿拍！</span>
            </div>
          </div>

          <div class="tip-item important">
            <div class="tip-number">5</div>
            <div class="tip-content">
              <span class="tip-title">[充值失败]</span>
              <span>网络充值存在10%-20%的失败率充值失败订单会在未来24小时内款项会原路退回，请注意查收;</span>
            </div>
          </div>

          <div class="tip-item important">
            <div class="tip-number">6</div>
            <div class="tip-content">
              <span>携号转网，充值号码错误，空号，虚商号码，企业号，号码停机，未实名号码勿拍，因为这些原因导致的充值失败，概不负责。</span>
            </div>
          </div>

          <div class="tip-item important">
            <div class="tip-number">7</div>
            <div class="tip-content">
              <span>不支持充值后撤销订单，充值之后等待到账即可。</span>
            </div>
          </div>
        </div>

        <!-- 特别注意 -->
        <div class="section">
          <div class="section-title warning">
            <i class="el-icon-warning"></i>
            <span>注意⚠️</span>
          </div>

          <div class="warning-content">
            电信手机号请月中进行充值！月初月尾充值会非常慢到账！介意慎拍！！！电信已更新支付13个地区：
            广东， 江苏， 河北， 江西， 河南， 甘肃， 湖北， 四川， 福建，
            吉林， 辽宁， 山东， 贵州 开启时间为每日7: 30-晚22: 00)
            其他地区在更新中 移动、联通支持全国！
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="footer-container">
      <el-button type="primary" size="large" @click="toNext" class="next-btn">
        下一步
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: "CreditWarning",
  mounted() {
    // 页面加载时设置token
    this.setTokenFromUrl();
  },
  methods: {
    // 从URL设置token到本地存储
    setTokenFromUrl() {
      const token = this.getUrlParam('token');
      const userId = this.getUrlParam('userId');
      const source = this.getUrlParam('source');

      console.log('Warning页面获取到的URL参数:', { token, userId, source });

      if (token) {
        // 设置到localStorage供axios拦截器使用
        localStorage.setItem('userToken', token);
        // 同时设置到cookies供PC端auth.js使用
        this.setTokenToCookie(token);
        console.log('Warning页面Token已设置:', token);
      }
      if (userId) {
        localStorage.setItem('userId', userId);
        this.setUserIdToCookie(userId);
        console.log('Warning页面UserId已设置:', userId);
      }
    },

    // 下一步
    toNext() {
      // 获取URL参数并传递给下一个页面
      const token = this.getUrlParam('token');
      const userId = this.getUrlParam('userId');
      const source = this.getUrlParam('source');

      if (token) {
        // 跳转到话费充值页面，携带参数 (使用hash路由)
        const params = [];
        params.push(`token=${encodeURIComponent(token)}`);
        if (userId) params.push(`userId=${encodeURIComponent(userId)}`);
        if (source) params.push(`source=${encodeURIComponent(source)}`);
        window.location.href = `/#/userCredit?${params.join('&')}`;
      } else {
        // 没有token，直接跳转 (使用hash路由)
        window.location.href = '/#/userCredit';
      }
    },

    // 设置token到cookies
    setTokenToCookie(token) {
      document.cookie = `token=${token}; path=/; max-age=86400`; // 24小时过期
    },

    // 设置userId到cookies
    setUserIdToCookie(userId) {
      document.cookie = `userid=${userId}; path=/; max-age=86400`; // 24小时过期
    },

    // 兼容hash路由和普通路由的URL参数获取方法
    getUrlParam(name) {
      // 首先尝试从hash后的参数获取（适用于hash路由）
      const hash = window.location.hash;
      if (hash && hash.includes('?')) {
        const hashParams = hash.split('?')[1];
        const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
        const r = hashParams.match(reg);
        if (r != null) return decodeURIComponent(r[2]);
      }

      // 然后尝试从search参数获取（适用于普通路由）
      const search = window.location.search.substring(1);
      if (search) {
        const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
        const r = search.match(reg);
        if (r != null) return decodeURIComponent(r[2]);
      }

      return null;
    },
  },
};
</script>

<style lang="scss" scoped>
.credit-warn-container {
  min-height: 100vh;
  background: #f5f5f5;
  position: relative;
  box-sizing: border-box;
}

/* 主内容区域 */
.main-content {
  padding: 20px;
  padding-bottom: 120px;
  /* 增加底部间距，避免被按钮遮挡 */
}

/* 内容卡片 */
.content-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

/* 分区样式 */
.section {
  margin-bottom: 25px;
}

.section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;

  i {
    color: #00d4aa;
  }
}

.section-title.warning {
  color: #f97316;

  i {
    color: #f97316;
  }
}

/* 提示项样式 */
.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 15px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #e9ecef;
  transition: all 0.3s ease;
}

.tip-item.important {
  background: #fff7ed;
  border-left-color: #f97316;
}

.tip-number {
  min-width: 20px;
  height: 20px;
  background: #00d4aa;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  margin-top: 2px;
}

.tip-item.important .tip-number {
  background: #f97316;
}

.tip-content {
  flex: 1;
  line-height: 1.6;
  font-size: 14px;
  color: #333;
}

.tip-title {
  font-weight: 600;
  color: #333;
  margin-right: 4px;
}

.tip-item.important .tip-title {
  color: #f97316;
}

/* 通用提示 */
.general-tip {
  background: #f0f9ff;
  border: 1px solid #e0f2fe;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  color: #0369a1;
  line-height: 1.6;
  margin-top: 10px;
}

/* 警告内容 */
.warning-content {
  background: #fff7ed;
  border: 1px solid #fed7aa;
  border-radius: 8px;
  padding: 15px;
  font-size: 14px;
  color: #ea580c;
  line-height: 1.6;
  font-weight: 500;
}

/* 底部按钮容器 */
.footer-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20px;
  border-top: 1px solid #f0f0f0;
  z-index: 1000;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);
}

.next-btn {
  width: 100%;
  height: 50px;
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 1px;
}

.next-btn:hover {
  background: linear-gradient(135deg, #00a389 0%, #008a73 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 15px;
    padding-bottom: 100px;
    /* 移动端稍微减少底部间距 */
  }

  .content-card {
    padding: 15px;
  }

  .footer-container {
    padding: 15px 20px;
    /* 移动端减少内边距 */
  }
}
</style>
